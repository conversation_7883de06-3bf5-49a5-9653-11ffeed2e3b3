<?php

namespace App\Http\Controllers;

use App\Http\Requests\SellerRegistrationRequest;
use App\Models\City;
use Illuminate\Http\Request;
use App\Models\Shop;
use App\Models\User;
use App\Models\BusinessSetting;
use Auth;
use Hash;
use App\Notifications\EmailVerificationNotification;
use Illuminate\Support\Facades\Notification;
use App\Utility\SendSMSUtility;

class ShopController extends Controller
{

    public function __construct()
    {
        $this->middleware('user', ['only' => ['index']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $shop = Auth::user()->shop;
        $cities = City::where('status', 1)->get();

        return view('seller.shop', compact('shop', 'cities'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $cities = City::where('status', 1)->get();
        if (Auth::check()) {
            if ((Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'customer')) {
                flash(translate('Admin or Customer cannot be a seller'))->error();
                return back();
            }
            if (Auth::user()->user_type == 'seller') {
                flash(translate('This user already a seller'))->error();
                return back();
            }
        } else {
            return view('auth.'.get_setting('authentication_layout_select').'.seller_registration', compact('cities'));
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(SellerRegistrationRequest $request)
    {
        // Check email uniqueness
        if(User::where('email', $request->email)->first() != null){
            flash(translate('Email already exists.'));
            return back();
        }

        // Check phone uniqueness if OTP system is enabled and phone is provided
        if (get_setting('otp_system') && $request->phone) {
            $countryCode = '+' . ltrim($request->country_code ?? '91', '+');
            $phone = str_replace($countryCode, '', $request->phone);

            if (User::where('phone', $phone)
                     ->where('country_code', $countryCode)
                     ->first() != null) {
                flash(translate('Phone number already exists.'));
                return back();
            }
        }

        $user = new User;
        $user->name = $request->name;
        $user->email = $request->email;
        $user->user_type = "seller";
        $user->password = Hash::make($request->password);

        // Add phone data if OTP system is enabled
        if (get_setting('otp_system') && $request->phone) {
            $countryCode = '+' . ltrim($request->country_code ?? '91', '+');
            // Remove country code from phone if it exists
            $phone = str_replace($countryCode, '', $request->phone);

            $user->phone = $phone;
            $user->country_code = $countryCode;
            $user->verification_code = rand(100000, 999999);
        }

        if ($user->save()) {
            // Send OTP if phone is provided and OTP system is enabled
            if (get_setting('otp_system') && $request->phone) {
                SendSMSUtility::sendOtpViaBoth($user->phone, $user->verification_code, $user->country_code);
            }
            $shop = new Shop;
            $shop->user_id = $user->id;
            $shop->name = $request->shop_name;
            $shop->address = $request->address;
            $shop->gst_number = $request->gst_number;
            $shop->city_id = $request->city_id;
            $shop->latitude = $request->latitude;
            $shop->longitude = $request->longitude;
            $shop->slug = preg_replace('/\s+/', '-', str_replace("/", " ", $request->shop_name));
            $shop->save();

            auth()->login($user, false);

            // Handle verification based on OTP system
            if (get_setting('otp_system') && $user->phone && !$user->phone_verified_at) {
                // Redirect to phone verification for sellers
                flash(translate('Registration successful! Please verify your phone number.'))->success();
                return redirect()->route('phone.verification');
            } else {
                // Handle email verification
                if (BusinessSetting::where('type', 'email_verification')->first()->value == 0) {
                    $user->email_verified_at = date('Y-m-d H:m:s');
                    $user->save();
                } else {
                    $user->notify(new EmailVerificationNotification());
                }

                flash(translate('Your Shop has been created successfully!'))->success();
                return redirect()->route('seller.shop.index');
            }
        }

        flash(translate('Sorry! Something went wrong.'))->error();
        return back();
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    public function destroy($id)
    {
        //
    }
}
